const express = require('express');
const summaryController = require('../controllers/summaryController');
const authController = require('../controllers/authController');

const router = express.Router();

// Protect all routes
router.use(authController.protect);

// Get monthly summary
router.get('/monthly', summaryController.getMonthlySummary);

// Get yearly summary
router.get('/yearly', summaryController.getYearlySummary);

module.exports = router;
