// Simple script to test your API endpoints
// Run with: node test-api.js
// For deployed version: API_URL=https://your-app.vercel.app node test-api.js

const baseUrl = process.env.API_URL || "http://localhost:3000";

async function testEndpoint(method, endpoint, data = null) {
  try {
    const options = {
      method,
      headers: {
        "Content-Type": "application/json",
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${baseUrl}${endpoint}`, options);
    const result = await response.json();

    console.log(`\n${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log("Response:", JSON.stringify(result, null, 2));

    return { status: response.status, data: result };
  } catch (error) {
    console.error(`Error testing ${method} ${endpoint}:`, error.message);
    return { error: error.message };
  }
}

async function runTests() {
  console.log(`Testing API at: ${baseUrl}`);
  console.log("=".repeat(50));

  // Test root endpoint
  await testEndpoint("GET", "/");

  // Test health check (most important for database status)
  console.log("\n🔍 Checking database connection...");
  const healthResult = await testEndpoint("GET", "/api/health");

  if (healthResult.data && healthResult.data.database) {
    const dbStatus = healthResult.data.database;
    if (dbStatus.connected && dbStatus.status === "connected") {
      console.log("✅ Database connection: HEALTHY");
    } else {
      console.log("❌ Database connection: FAILED");
      console.log(`   Status: ${dbStatus.status}`);
      console.log(`   ReadyState: ${dbStatus.readyState}`);
    }
  }

  // Test a non-existent route (should return 404)
  await testEndpoint("GET", "/api/nonexistent");

  console.log("\n" + "=".repeat(50));
  console.log("Basic API tests completed!");
  console.log("\n📋 Next steps:");
  console.log(
    "1. If database shows as disconnected, check your MONGO_URI environment variable"
  );
  console.log("2. Verify MongoDB Atlas network access settings");
  console.log("3. For full testing, use Postman or create a proper test suite");
  console.log("4. See DATABASE_TROUBLESHOOTING.md for detailed help");
}

// Run the tests
runTests().catch(console.error);
