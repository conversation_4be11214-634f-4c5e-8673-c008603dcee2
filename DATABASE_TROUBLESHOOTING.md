# Database Connection Troubleshooting Guide

## Common Database Connection Issues and Solutions

### 1. "Database service temporarily unavailable" Error

This error occurs when the API cannot connect to MongoDB. Here are the most common causes and solutions:

#### **Cause 1: Missing Environment Variables**

**Problem**: `MONGO_URI` not set in Vercel environment variables.

**Solution**:
1. Go to your Vercel project dashboard
2. Navigate to "Settings" > "Environment Variables"
3. Add `MONGO_URI` with your MongoDB connection string
4. Redeploy your project

**Example MongoDB URI**:
```
mongodb+srv://username:<EMAIL>/room-expense-tracker?retryWrites=true&w=majority
```

#### **Cause 2: MongoDB Atlas Network Access**

**Problem**: Vercel's IP addresses are not whitelisted in MongoDB Atlas.

**Solution**:
1. Go to MongoDB Atlas dashboard
2. Navigate to "Network Access"
3. Click "Add IP Address"
4. Add `0.0.0.0/0` to allow all IPs (for Vercel serverless functions)
5. Or add specific Vercel IP ranges if you prefer more security

#### **Cause 3: Incorrect Database Credentials**

**Problem**: Wrong username, password, or database name in connection string.

**Solution**:
1. Verify your MongoDB Atlas credentials
2. Check if the database user has proper permissions
3. Ensure the database name in the connection string exists
4. Test the connection string locally first

#### **Cause 4: MongoDB Atlas Cluster Issues**

**Problem**: MongoDB Atlas cluster is paused or having issues.

**Solution**:
1. Check MongoDB Atlas status page
2. Ensure your cluster is not paused (free tier clusters pause after inactivity)
3. Try connecting from MongoDB Compass to verify cluster status

### 2. Connection Timeout Issues

#### **Symptoms**:
- Slow API responses
- Intermittent connection failures
- Timeout errors in logs

#### **Solutions**:

**1. Optimize Connection Settings**:
The API is already configured with optimized settings:
```javascript
{
  serverSelectionTimeoutMS: 5000, // 5 second timeout
  socketTimeoutMS: 45000,         // 45 second socket timeout
  maxPoolSize: 10,                // Max 10 connections
  minPoolSize: 1,                 // Min 1 connection
}
```

**2. Check MongoDB Atlas Region**:
- Ensure your MongoDB cluster is in a region close to Vercel's deployment region
- Consider upgrading to a dedicated cluster for better performance

### 3. Testing Database Connection

#### **Test Health Endpoint**:
```bash
curl https://your-vercel-app.vercel.app/api/health
```

**Expected Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-20T10:30:00.000Z",
  "environment": "production",
  "database": {
    "status": "connected",
    "readyState": 1,
    "connected": true
  },
  "version": "1.0.0"
}
```

#### **Database Status Meanings**:
- `readyState: 0` = disconnected
- `readyState: 1` = connected ✅
- `readyState: 2` = connecting
- `readyState: 3` = disconnecting

### 4. Local Testing

#### **Test Locally with Vercel Dev**:
```bash
# Install Vercel CLI
npm install -g vercel

# Create local .env file
cp .env.example .env
# Edit .env with your actual values

# Run locally with Vercel environment
vercel dev
```

#### **Test with Node.js**:
```bash
# Run the original server.js
npm run dev
```

### 5. Environment Variables Checklist

Ensure these are set in Vercel:

| Variable | Required | Example |
|----------|----------|---------|
| `MONGO_URI` | ✅ | `mongodb+srv://user:<EMAIL>/dbname` |
| `JWT_SECRET` | ✅ | `your-super-secret-jwt-key-here` |
| `NODE_ENV` | ✅ | `production` |

### 6. MongoDB Atlas Setup Checklist

- [ ] Cluster is running (not paused)
- [ ] Database user created with read/write permissions
- [ ] Network access allows Vercel IPs (`0.0.0.0/0`)
- [ ] Connection string is correct
- [ ] Database name exists

### 7. Vercel Deployment Checklist

- [ ] Environment variables are set
- [ ] Latest code is deployed
- [ ] No build errors in deployment logs
- [ ] Function logs show successful deployment

### 8. Advanced Debugging

#### **Check Vercel Function Logs**:
1. Go to Vercel dashboard
2. Navigate to "Functions" tab
3. Click on your API function
4. Check the logs for connection errors

#### **Common Log Messages**:
- `"Connected to MongoDB"` ✅ Good
- `"MONGO_URI not found"` ❌ Missing environment variable
- `"MongoDB connection error"` ❌ Connection failed
- `"serverSelectionTimeoutMS"` ❌ Cannot reach MongoDB

### 9. Emergency Fallback

If database issues persist, the API includes fallback behavior:

- **Development**: Returns mock data for testing
- **Production**: Returns proper error messages with retry instructions

### 10. Getting Help

If you're still experiencing issues:

1. **Check Vercel Status**: https://vercel.com/status
2. **Check MongoDB Atlas Status**: https://status.cloud.mongodb.com/
3. **Test Connection**: Use MongoDB Compass to test your connection string
4. **Contact Support**: Provide error logs and environment details

### Quick Fix Commands

```bash
# Redeploy to Vercel
vercel --prod

# Test health endpoint
curl https://your-app.vercel.app/api/health

# Check environment variables
vercel env ls

# View function logs
vercel logs
```

Remember: After making any changes to environment variables in Vercel, you need to redeploy your application for the changes to take effect!
