const Expense = require('../models/expenseModel');

// Get all expenses
exports.getAllExpenses = async (req, res, next) => {
  try {
    // Build query
    let query = Expense.find();

    // Filter by user if specified
    if (req.query.user) {
      query = query.find({ user: req.query.user });
    }

    // Filter by date range
    if (req.query.startDate && req.query.endDate) {
      query = query.find({
        date: {
          $gte: new Date(req.query.startDate),
          $lte: new Date(req.query.endDate),
        },
      });
    }

    // Filter by month and year
    if (req.query.month && req.query.year) {
      const month = parseInt(req.query.month);
      const year = parseInt(req.query.year);
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);

      query = query.find({
        date: {
          $gte: startDate,
          $lte: endDate,
        },
      });
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-date');
    }

    // Execute query
    const expenses = await query;

    // Send response
    res.status(200).json({
      status: 'success',
      results: expenses.length,
      data: {
        expenses,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Get expense by ID
exports.getExpense = async (req, res, next) => {
  try {
    const expense = await Expense.findById(req.params.id);

    if (!expense) {
      return res.status(404).json({
        status: 'error',
        message: 'No expense found with that ID',
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        expense,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Create new expense
exports.createExpense = async (req, res, next) => {
  try {
    // If user is not specified, use the logged-in user
    if (!req.body.user) {
      req.body.user = req.user.id;
    }

    const newExpense = await Expense.create(req.body);

    res.status(201).json({
      status: 'success',
      data: {
        expense: newExpense,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Update expense
exports.updateExpense = async (req, res, next) => {
  try {
    const expense = await Expense.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    if (!expense) {
      return res.status(404).json({
        status: 'error',
        message: 'No expense found with that ID',
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        expense,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Delete expense
exports.deleteExpense = async (req, res, next) => {
  try {
    const expense = await Expense.findByIdAndDelete(req.params.id);

    if (!expense) {
      return res.status(404).json({
        status: 'error',
        message: 'No expense found with that ID',
      });
    }

    res.status(204).json({
      status: 'success',
      data: null,
    });
  } catch (err) {
    next(err);
  }
};

// Get my expenses
exports.getMyExpenses = async (req, res, next) => {
  try {
    const expenses = await Expense.find({ user: req.user.id });

    res.status(200).json({
      status: 'success',
      results: expenses.length,
      data: {
        expenses,
      },
    });
  } catch (err) {
    next(err);
  }
};
