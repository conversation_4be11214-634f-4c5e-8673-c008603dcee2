const express = require("express");
const authController = require("../controllers/authController");

const router = express.Router();

// Public routes
router.post("/signup", authController.signup);
router.post("/login", authController.login);

// Protected routes (require authentication)
router.use(authController.protect);
router.patch("/update-password", authController.updatePassword);

module.exports = router;
