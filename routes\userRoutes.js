const express = require("express");
const userController = require("../controllers/userController");
const authController = require("../controllers/authController");

const router = express.Router();

// Protected routes - all routes require authentication
router.use(authController.protect);

// Routes for all authenticated users
router.get("/me", userController.getMe);
router.patch("/updateMe", userController.updateMe);

// Allow all authenticated users to get the list of users
router.get("/", userController.getAllUsers);

// Admin only routes
router.use(authController.restrictTo("admin"));

// These routes are restricted to admin users
router.post("/", userController.createUser);

router
  .route("/:id")
  .get(userController.getUser)
  .patch(userController.updateUser)
  .delete(userController.deleteUser);

module.exports = router;
