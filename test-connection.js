// Test script to verify MongoDB connection
const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function testConnection() {
  console.log('Testing MongoDB connection...');
  console.log('MONGO_URI exists:', !!process.env.MONGO_URI);
  console.log('NODE_ENV:', process.env.NODE_ENV);
  
  if (!process.env.MONGO_URI) {
    console.error('❌ MONGO_URI not found in environment variables');
    return;
  }

  try {
    console.log('Attempting to connect to MongoDB...');
    
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // 5 second timeout
    });

    console.log('✅ Successfully connected to MongoDB');
    console.log('Database name:', mongoose.connection.db.databaseName);
    console.log('Connection state:', mongoose.connection.readyState);
    
    // Test a simple operation
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('Available collections:', collections.map(c => c.name));
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:');
    console.error('Error name:', error.name);
    console.error('Error message:', error.message);
    
    if (error.name === 'MongoServerSelectionError') {
      console.error('\n🔍 Possible causes:');
      console.error('1. MongoDB Atlas cluster is paused');
      console.error('2. Network access not configured (add 0.0.0.0/0)');
      console.error('3. Incorrect connection string');
      console.error('4. Database credentials are wrong');
    }
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
testConnection().catch(console.error);
