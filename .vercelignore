# Dependencies
node_modules/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files (optional - remove if you want to include tests)
test/
tests/
*.test.js
*.spec.js

# Documentation (optional - remove if you want to include docs)
docs/
README.md
VERCEL_DEPLOYMENT_GUIDE.md

# Local development files
test-api.js
