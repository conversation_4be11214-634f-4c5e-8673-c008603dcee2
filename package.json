{"name": "room-expense-tracker-backend", "version": "1.0.0", "description": "Backend for Room Expense Tracker App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "cross-env HOST=0.0.0.0 PORT=5000 node server.js", "vercel-dev": "vercel dev", "test": "node test-api.js", "test-local": "API_URL=http://localhost:3000 node test-api.js", "build": "echo 'No build step required for Node.js'"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "validator": "^13.11.0", "serverless-http": "^3.0.2"}, "devDependencies": {"cross-env": "^7.0.3", "nodemon": "^3.0.2"}}