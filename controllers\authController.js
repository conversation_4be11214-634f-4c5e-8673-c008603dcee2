const jwt = require("jsonwebtoken");
const { promisify } = require("util");
const mongoose = require("mongoose");
const User = require("../models/userModel");

// Create JWT token
const signToken = (id) => {
  return jwt.sign({ id }, "room_expense_tracker_secret_key_2024", {
    expiresIn: "7d", // 7 days
  });
};

// Send JWT token in response
const createSendToken = (user, statusCode, res) => {
  const token = signToken(user._id);

  // Remove password from output
  user.password = undefined;

  // Add isAdmin flag for frontend
  const isAdmin = user.role === "admin";

  res.status(statusCode).json({
    status: "success",
    token,
    data: {
      user,
      isAdmin,
    },
  });
};

// Register a new user
exports.signup = async (req, res, next) => {
  try {
    console.log("Signup request received:", req.body);
    console.log(
      "MongoDB connection readyState:",
      mongoose.connection.readyState
    );

    // Ensure connection is ready before proceeding
    if (mongoose.connection.readyState !== 1) {
      console.log("Waiting for MongoDB connection...");
      await new Promise((resolve, reject) => {
        if (mongoose.connection.readyState === 1) {
          resolve();
        } else {
          mongoose.connection.once("connected", resolve);
          mongoose.connection.once("error", reject);
          // Timeout after 10 seconds
          setTimeout(() => reject(new Error("Connection timeout")), 10000);
        }
      });
      console.log(
        "MongoDB connection ready, readyState:",
        mongoose.connection.readyState
      );
    }

    // Validate required fields
    if (
      !req.body.name ||
      !req.body.email ||
      !req.body.password ||
      !req.body.passwordConfirm
    ) {
      return res.status(400).json({
        status: "error",
        message:
          "Please provide name, email, password and password confirmation",
      });
    }

    // Check if passwords match
    if (req.body.password !== req.body.passwordConfirm) {
      return res.status(400).json({
        status: "error",
        message: "Passwords do not match",
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: req.body.email });
    if (existingUser) {
      return res.status(400).json({
        status: "error",
        message: "Email already in use",
      });
    }

    // Create new user
    const newUser = await User.create({
      name: req.body.name,
      email: req.body.email,
      phoneNumber: req.body.phoneNumber,
      password: req.body.password,
      passwordConfirm: req.body.passwordConfirm,
      role: req.body.role || "user",
    });

    console.log("User created successfully:", newUser);
    createSendToken(newUser, 201, res);
  } catch (err) {
    console.error("Error in signup:", err);

    // Send detailed error message
    res.status(400).json({
      status: "error",
      message: err.message,
      error: err,
    });
  }
};

// Login user
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Check if email and password exist
    if (!email || !password) {
      return res.status(400).json({
        status: "error",
        message: "Please provide email and password",
      });
    }

    console.log("Login attempt for:", email);
    console.log(
      "MongoDB connection readyState:",
      mongoose.connection.readyState
    );

    // Check if user exists && password is correct
    const user = await User.findOne({ email }).select("+password");

    if (!user || !(await user.correctPassword(password, user.password))) {
      return res.status(401).json({
        status: "error",
        message: "Incorrect email or password",
      });
    }

    // If everything ok, send token to client
    createSendToken(user, 200, res);
  } catch (err) {
    next(err);
  }
};

// Protect routes - middleware to check if user is logged in
exports.protect = async (req, res, next) => {
  try {
    // Get token from header
    let token;
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith("Bearer")
    ) {
      token = req.headers.authorization.split(" ")[1];
    }

    // For development/testing - allow a test token
    if (process.env.NODE_ENV === "development" && !token) {
      token = "test-token";
      // Skip verification for test token
      req.user = {
        id: "123456789012",
        name: "Test User",
        email: "<EMAIL>",
        role: "user",
      };
      return next();
    }

    if (!token) {
      return res.status(401).json({
        status: "error",
        message: "You are not logged in! Please log in to get access.",
      });
    }

    try {
      // Verify token
      const decoded = await promisify(jwt.verify)(
        token,
        "room_expense_tracker_secret_key_2024"
      );

      // Check if user still exists
      const currentUser = await User.findById(decoded.id);
      if (!currentUser) {
        return res.status(401).json({
          status: "error",
          message: "The user belonging to this token no longer exists.",
        });
      }

      // Grant access to protected route
      req.user = currentUser;
      next();
    } catch (verifyError) {
      // For development/testing - if token verification fails, still allow access with test user
      if (process.env.NODE_ENV === "development") {
        console.log("Token verification failed, using test user");
        req.user = {
          id: "123456789012",
          name: "Test User",
          email: "<EMAIL>",
          role: "user",
        };
        return next();
      }

      return res.status(401).json({
        status: "error",
        message: "Invalid token. Please log in again.",
      });
    }
  } catch (err) {
    next(err);
  }
};

// Restrict to certain roles
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        status: "error",
        message: "You do not have permission to perform this action",
      });
    }
    next();
  };
};

// Update password for logged in user
exports.updatePassword = async (req, res, next) => {
  try {
    // 1) Get user from collection
    const user = await User.findById(req.user.id).select("+password");

    // 2) Check if POSTed current password is correct
    if (
      !(await user.correctPassword(req.body.currentPassword, user.password))
    ) {
      return res.status(401).json({
        status: "error",
        message: "Your current password is incorrect",
      });
    }

    // 3) If so, update password
    user.password = req.body.newPassword;
    user.passwordConfirm = req.body.newPasswordConfirm;

    // 4) Set isAdminCreated to false since user is changing their password
    user.isAdminCreated = false;

    await user.save();

    // 5) Log user in, send JWT
    createSendToken(user, 200, res);
  } catch (err) {
    console.error("Error updating password:", err);
    res.status(400).json({
      status: "error",
      message: err.message,
    });
  }
};
