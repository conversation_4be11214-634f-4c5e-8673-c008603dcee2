const Expense = require('../models/expenseModel');
const User = require('../models/userModel');

// Get monthly summary
exports.getMonthlySummary = async (req, res, next) => {
  try {
    const { month, year } = req.query;
    
    if (!month || !year) {
      return res.status(400).json({
        status: 'error',
        message: 'Please provide month and year',
      });
    }

    // Convert to integers
    const monthInt = parseInt(month);
    const yearInt = parseInt(year);

    // Validate month and year
    if (monthInt < 1 || monthInt > 12 || yearInt < 2000 || yearInt > 2100) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid month or year',
      });
    }

    // Calculate date range for the month
    const startDate = new Date(yearInt, monthInt - 1, 1);
    const endDate = new Date(yearInt, monthInt, 0);

    // Get all expenses for the month
    const expenses = await Expense.find({
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    // Get all users
    const users = await User.find();

    // Calculate total amount
    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Calculate per head amount
    const userCount = users.length;
    const perHeadAmount = userCount > 0 ? totalAmount / userCount : 0;

    // Calculate user expenses
    const userExpenses = {};
    users.forEach(user => {
      userExpenses[user._id] = {
        userId: user._id,
        name: user.name,
        email: user.email,
        spent: 0,
        balance: 0,
      };
    });

    // Sum up expenses by user
    expenses.forEach(expense => {
      const userId = expense.user._id.toString();
      if (userExpenses[userId]) {
        userExpenses[userId].spent += expense.amount;
      }
    });

    // Calculate balance for each user
    Object.keys(userExpenses).forEach(userId => {
      userExpenses[userId].balance = userExpenses[userId].spent - perHeadAmount;
    });

    // Create summary object
    const summary = {
      month: monthInt,
      year: yearInt,
      totalAmount,
      userCount,
      perHeadAmount,
      userExpenses: Object.values(userExpenses),
    };

    res.status(200).json({
      status: 'success',
      data: {
        summary,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Get yearly summary
exports.getYearlySummary = async (req, res, next) => {
  try {
    const { year } = req.query;
    
    if (!year) {
      return res.status(400).json({
        status: 'error',
        message: 'Please provide year',
      });
    }

    // Convert to integer
    const yearInt = parseInt(year);

    // Validate year
    if (yearInt < 2000 || yearInt > 2100) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid year',
      });
    }

    // Calculate date range for the year
    const startDate = new Date(yearInt, 0, 1);
    const endDate = new Date(yearInt, 11, 31);

    // Get all expenses for the year
    const expenses = await Expense.find({
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    });

    // Group expenses by month
    const monthlyExpenses = {};
    for (let i = 1; i <= 12; i++) {
      monthlyExpenses[i] = 0;
    }

    expenses.forEach(expense => {
      const month = expense.date.getMonth() + 1;
      monthlyExpenses[month] += expense.amount;
    });

    // Create summary object
    const summary = {
      year: yearInt,
      totalAmount: expenses.reduce((sum, expense) => sum + expense.amount, 0),
      monthlyExpenses,
    };

    res.status(200).json({
      status: 'success',
      data: {
        summary,
      },
    });
  } catch (err) {
    next(err);
  }
};
