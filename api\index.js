const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const morgan = require("morgan");
const dotenv = require("dotenv");

// Load environment variables
// In Vercel, environment variables are automatically available
// Only try to load .env file in local development
if (process.env.NODE_ENV !== "production") {
  const path = require("path");
  const result = dotenv.config({ path: path.join(__dirname, "../.env") });
  if (result.error) {
    console.error("Error loading .env file:", result.error);
  }
}

// Log environment status for debugging
console.log("Environment check:");
console.log("NODE_ENV:", process.env.NODE_ENV);
console.log("MONGO_URI exists:", !!process.env.MONGO_URI);
console.log("JWT_SECRET exists:", !!process.env.JWT_SECRET);

// Set environment
process.env.NODE_ENV = process.env.NODE_ENV || "production";

// Import routes
const userRoutes = require("../routes/userRoutes");
const expenseRoutes = require("../routes/expenseRoutes");
const authRoutes = require("../routes/authRoutes");
const summaryRoutes = require("../routes/summaryRoutes");

// Create Express app
const app = express();

// Middleware
app.use(
  cors({
    origin: "*", // Allow all origins
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());

// Only use morgan in development
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Routes
app.use("/api/users", userRoutes);
app.use("/api/expenses", expenseRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/summary", summaryRoutes);

// Root route
app.get("/", (req, res) => {
  res.json({
    message: "Room Expense Tracker API is running...",
    status: "success",
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  });
});

// Health check route
app.get("/api/health", async (req, res) => {
  const dbStatus = mongoose.connection.readyState;
  const dbStatusText = {
    0: "disconnected",
    1: "connected",
    2: "connecting",
    3: "disconnecting",
  };

  // Try to connect if not connected
  let connectionAttempted = false;
  let connectionResult = false;

  if (dbStatus !== 1) {
    console.log("🔄 Health check: Attempting database connection...");
    connectionAttempted = true;
    connectionResult = await connectToDatabase();
  }

  const finalDbStatus = mongoose.connection.readyState;

  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    database: {
      status: dbStatusText[finalDbStatus] || "unknown",
      readyState: finalDbStatus,
      connected: finalDbStatus === 1,
      connectionAttempted,
      connectionResult,
    },
    environmentVariables: {
      NODE_ENV: process.env.NODE_ENV,
      MONGO_URI_EXISTS: !!process.env.MONGO_URI,
      JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
      MONGO_URI_LENGTH: process.env.MONGO_URI
        ? process.env.MONGO_URI.length
        : 0,
    },
    version: "1.0.0",
  });
});

// Debug endpoint to check environment variables
app.get("/api/debug/env", (req, res) => {
  res.json({
    NODE_ENV: process.env.NODE_ENV,
    MONGO_URI_EXISTS: !!process.env.MONGO_URI,
    JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
    MONGO_URI_LENGTH: process.env.MONGO_URI ? process.env.MONGO_URI.length : 0,
    MONGO_URI_PREVIEW: process.env.MONGO_URI
      ? process.env.MONGO_URI.substring(0, 50) + "..."
      : "NOT_FOUND",
    ALL_ENV_KEYS: Object.keys(process.env).filter(
      (key) =>
        key.includes("MONGO") || key.includes("JWT") || key.includes("NODE_ENV")
    ),
  });
});

// Test MongoDB connection endpoint
app.get("/api/debug/test-connection", async (req, res) => {
  try {
    console.log("🧪 Testing MongoDB connection directly...");

    if (!process.env.MONGO_URI) {
      return res.status(500).json({
        success: false,
        error: "MONGO_URI not found",
      });
    }

    // Test connection with detailed error reporting
    const testConnection = async () => {
      const testMongoose = require("mongoose");

      try {
        await testMongoose.connect(process.env.MONGO_URI, {
          serverSelectionTimeoutMS: 15000, // 15 second timeout for testing
          socketTimeoutMS: 45000,
          maxPoolSize: 1, // Single connection for testing
        });

        const dbName = testMongoose.connection.db.databaseName;
        const collections = await testMongoose.connection.db
          .listCollections()
          .toArray();

        await testMongoose.disconnect();

        return {
          success: true,
          database: dbName,
          collections: collections.map((c) => c.name),
          connectionString: process.env.MONGO_URI.substring(0, 50) + "...",
        };
      } catch (error) {
        await testMongoose.disconnect();
        throw error;
      }
    };

    const result = await testConnection();
    res.json(result);
  } catch (error) {
    console.error("❌ Connection test failed:", error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      errorName: error.name,
      errorCode: error.code,
      connectionString: process.env.MONGO_URI
        ? process.env.MONGO_URI.substring(0, 50) + "..."
        : "NOT_FOUND",
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  res.status(statusCode).json({
    status: "error",
    statusCode,
    message: err.message,
    stack: process.env.NODE_ENV === "production" ? null : err.stack,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    status: "error",
    statusCode: 404,
    message: "Route not found",
    path: req.path,
  });
});

// Connect to MongoDB (for serverless, we'll connect on each request)
let isConnected = false;

const connectToDatabase = async () => {
  // Check if we already have a good connection
  if (mongoose.connection.readyState === 1) {
    console.log("✅ Using existing MongoDB connection");
    return true;
  }

  try {
    if (!process.env.MONGO_URI) {
      console.warn("MONGO_URI not found in environment variables");
      return false;
    }

    console.log("🔄 Establishing new MongoDB connection...");
    console.log("Current readyState:", mongoose.connection.readyState);

    // Force disconnect and create fresh connection like the working test
    if (mongoose.connection.readyState !== 0) {
      console.log("🔄 Disconnecting existing connection...");
      await mongoose.disconnect();
      await new Promise((resolve) => setTimeout(resolve, 200));
    }

    // Use the exact same approach as the working test connection
    await mongoose.connect(process.env.MONGO_URI, {
      serverSelectionTimeoutMS: 15000, // Same as test
      socketTimeoutMS: 45000,
      maxPoolSize: 1, // Same as test - single connection
    });

    isConnected = true;
    console.log("✅ Successfully connected to MongoDB");
    console.log("Database name:", mongoose.connection.db.databaseName);
    console.log("Final readyState:", mongoose.connection.readyState);
    return true;
  } catch (error) {
    console.error("❌ MongoDB connection failed:");
    console.error("Error name:", error.name);
    console.error("Error message:", error.message);
    console.error("Error code:", error.code);

    // Try to disconnect on error
    try {
      await mongoose.disconnect();
    } catch (disconnectError) {
      console.error("Error during disconnect:", disconnectError.message);
    }

    isConnected = false;
    return false;
  }
};

// Middleware to ensure database connection
app.use(async (req, res, next) => {
  const connected = await connectToDatabase();
  req.dbConnected = connected;
  next();
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("UNHANDLED REJECTION:", err.name, err.message);
});

// Export the Express app for Vercel
module.exports = app;
