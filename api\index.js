const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
const morgan = require("morgan");
const dotenv = require("dotenv");

// Load environment variables
// In Vercel, environment variables are automatically available
// Only try to load .env file in local development
if (process.env.NODE_ENV !== "production") {
  const path = require("path");
  const result = dotenv.config({ path: path.join(__dirname, "../.env") });
  if (result.error) {
    console.error("Error loading .env file:", result.error);
  }
}

// Log environment status for debugging
console.log("Environment check:");
console.log("NODE_ENV:", process.env.NODE_ENV);
console.log("MONGO_URI exists:", !!process.env.MONGO_URI);
console.log("JWT_SECRET exists:", !!process.env.JWT_SECRET);

// Set environment
process.env.NODE_ENV = process.env.NODE_ENV || "production";

// Import routes
const userRoutes = require("../routes/userRoutes");
const expenseRoutes = require("../routes/expenseRoutes");
const authRoutes = require("../routes/authRoutes");
const summaryRoutes = require("../routes/summaryRoutes");

// Create Express app
const app = express();

// Middleware
app.use(
  cors({
    origin: "*", // Allow all origins
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json());

// Only use morgan in development
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Routes
app.use("/api/users", userRoutes);
app.use("/api/expenses", expenseRoutes);
app.use("/api/auth", authRoutes);
app.use("/api/summary", summaryRoutes);

// Root route
app.get("/", (req, res) => {
  res.json({
    message: "Room Expense Tracker API is running...",
    status: "success",
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
  });
});

// Health check route
app.get("/api/health", (req, res) => {
  const dbStatus = mongoose.connection.readyState;
  const dbStatusText = {
    0: "disconnected",
    1: "connected",
    2: "connecting",
    3: "disconnecting",
  };

  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    database: {
      status: dbStatusText[dbStatus] || "unknown",
      readyState: dbStatus,
      connected: req.dbConnected || false,
    },
    environmentVariables: {
      NODE_ENV: process.env.NODE_ENV,
      MONGO_URI_EXISTS: !!process.env.MONGO_URI,
      JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
      MONGO_URI_LENGTH: process.env.MONGO_URI
        ? process.env.MONGO_URI.length
        : 0,
    },
    version: "1.0.0",
  });
});

// Debug endpoint to check environment variables
app.get("/api/debug/env", (req, res) => {
  res.json({
    NODE_ENV: process.env.NODE_ENV,
    MONGO_URI_EXISTS: !!process.env.MONGO_URI,
    JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
    MONGO_URI_LENGTH: process.env.MONGO_URI ? process.env.MONGO_URI.length : 0,
    MONGO_URI_PREVIEW: process.env.MONGO_URI
      ? process.env.MONGO_URI.substring(0, 50) + "..."
      : "NOT_FOUND",
    ALL_ENV_KEYS: Object.keys(process.env).filter(
      (key) =>
        key.includes("MONGO") || key.includes("JWT") || key.includes("NODE_ENV")
    ),
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  const statusCode = err.statusCode || 500;
  res.status(statusCode).json({
    status: "error",
    statusCode,
    message: err.message,
    stack: process.env.NODE_ENV === "production" ? null : err.stack,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    status: "error",
    statusCode: 404,
    message: "Route not found",
    path: req.path,
  });
});

// Connect to MongoDB (for serverless, we'll connect on each request)
let isConnected = false;

const connectToDatabase = async () => {
  if (isConnected && mongoose.connection.readyState === 1) {
    return true;
  }

  try {
    if (!process.env.MONGO_URI) {
      console.warn("MONGO_URI not found in environment variables");
      return false;
    }

    // Close existing connection if it's in a bad state
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }

    await mongoose.connect(process.env.MONGO_URI, {
      bufferCommands: false,
      bufferMaxEntries: 0,
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
      maxPoolSize: 10, // Maintain up to 10 socket connections
      minPoolSize: 1, // Maintain at least 1 socket connection
    });

    isConnected = true;
    console.log("Connected to MongoDB");
    return true;
  } catch (error) {
    console.error("MongoDB connection error:", error);
    isConnected = false;
    return false;
  }
};

// Middleware to ensure database connection
app.use(async (req, res, next) => {
  const connected = await connectToDatabase();
  req.dbConnected = connected;
  next();
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("UNHANDLED REJECTION:", err.name, err.message);
});

// Export the Express app for Vercel
module.exports = app;
