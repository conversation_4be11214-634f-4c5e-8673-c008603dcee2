# Room Expense Tracker Backend

This is the backend API for the Room Expense Tracker application. It provides endpoints for user management, expense tracking, and monthly/yearly summaries.

## Technologies Used

- Node.js
- Express.js
- MongoDB with Mongoose
- JWT Authentication
- bcrypt for password hashing

## Setup Instructions

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file in the root directory with the following variables:
   ```
   PORT=5000
   MONGO_URI=mongodb://localhost:27017/room_expense_tracker
   JWT_SECRET=your_jwt_secret_key_here
   JWT_EXPIRES_IN=30d
   ```

3. Start the server:
   ```
   npm run dev
   ```

## API Endpoints

### Authentication

- `POST /api/auth/signup` - Register a new user
- `POST /api/auth/login` - Login user

### Users

- `GET /api/users/me` - Get current user profile
- `PATCH /api/users/updateMe` - Update current user profile
- `GET /api/users` - Get all users (admin only)
- `POST /api/users` - Create a new user (admin only)
- `GET /api/users/:id` - Get user by ID (admin only)
- `PATCH /api/users/:id` - Update user (admin only)
- `DELETE /api/users/:id` - Delete user (admin only)

### Expenses

- `GET /api/expenses/my-expenses` - Get current user's expenses
- `GET /api/expenses` - Get all expenses (with filtering options)
- `POST /api/expenses` - Create a new expense
- `GET /api/expenses/:id` - Get expense by ID
- `PATCH /api/expenses/:id` - Update expense
- `DELETE /api/expenses/:id` - Delete expense

### Summary

- `GET /api/summary/monthly?month=5&year=2023` - Get monthly summary
- `GET /api/summary/yearly?year=2023` - Get yearly summary

## Query Parameters for Expenses

- `user` - Filter by user ID
- `startDate` and `endDate` - Filter by date range
- `month` and `year` - Filter by month and year
- `sort` - Sort results (e.g., `sort=date` or `sort=-amount`)

## Authentication

All routes except `/api/auth/signup` and `/api/auth/login` require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer your_jwt_token
```

## Error Handling

The API returns appropriate status codes and error messages for different scenarios:

- 200: Success
- 201: Created
- 204: No Content
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Server Error
