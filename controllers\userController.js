const User = require("../models/userModel");

// Get all users
exports.getAllUsers = async (req, res, next) => {
  try {
    // Get all users including the current admin
    const users = await User.find();

    // Log the current user and all users
    console.log("Current user (req.user):", req.user);
    console.log("All users found:", users.length);

    res.status(200).json({
      status: "success",
      results: users.length,
      data: {
        users,
        currentUser: req.user,
      },
    });
  } catch (err) {
    console.error("Error getting all users:", err);
    next(err);
  }
};

// Get user by ID
exports.getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        status: "error",
        message: "No user found with that ID",
      });
    }

    res.status(200).json({
      status: "success",
      data: {
        user,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Create new user (admin only)
exports.createUser = async (req, res, next) => {
  try {
    console.log("Admin creating user:", req.body);

    // Set isAdminCreated flag to true
    const userData = {
      ...req.body,
      isAdminCreated: true,
      // Set default password if not provided
      password: req.body.password || "password123",
      passwordConfirm: req.body.passwordConfirm || "password123",
    };

    const newUser = await User.create(userData);

    // Remove sensitive fields from response
    newUser.password = undefined;
    newUser.passwordConfirm = undefined;
    newUser.isAdminCreated = undefined;

    res.status(201).json({
      status: "success",
      data: {
        user: newUser,
        message: "User created successfully with default password: password123",
      },
    });
  } catch (err) {
    console.error("Error creating user:", err);
    res.status(400).json({
      status: "error",
      message: err.message,
    });
  }
};

// Update user
exports.updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    if (!user) {
      return res.status(404).json({
        status: "error",
        message: "No user found with that ID",
      });
    }

    res.status(200).json({
      status: "success",
      data: {
        user,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Delete user
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return res.status(404).json({
        status: "error",
        message: "No user found with that ID",
      });
    }

    res.status(204).json({
      status: "success",
      data: null,
    });
  } catch (err) {
    next(err);
  }
};

// Get current user profile
exports.getMe = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      status: "success",
      data: {
        user,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Update current user profile
exports.updateMe = async (req, res, next) => {
  try {
    // Create error if user tries to update password
    if (req.body.password || req.body.passwordConfirm) {
      return res.status(400).json({
        status: "error",
        message:
          "This route is not for password updates. Please use /updateMyPassword.",
      });
    }

    // Filter out unwanted fields that are not allowed to be updated
    const filteredBody = filterObj(req.body, "name", "email", "phoneNumber");

    // Update user document
    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      filteredBody,
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      status: "success",
      data: {
        user: updatedUser,
      },
    });
  } catch (err) {
    next(err);
  }
};

// Helper function to filter object
const filterObj = (obj, ...allowedFields) => {
  const newObj = {};
  Object.keys(obj).forEach((el) => {
    if (allowedFields.includes(el)) newObj[el] = obj[el];
  });
  return newObj;
};
