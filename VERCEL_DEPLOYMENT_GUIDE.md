# Vercel Deployment Guide for Room Expense Tracker Backend

## Prerequisites

1. **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
2. **MongoDB Atlas**: Set up a MongoDB Atlas cluster (free tier available)
3. **GitHub Repository**: Your code should be in a GitHub repository

## Step 1: Prepare Your Environment Variables

You'll need to set these environment variables in Vercel:

- `MONGO_URI`: Your MongoDB connection string
- `JWT_SECRET`: A secure random string for JWT token signing
- `NODE_ENV`: Set to "production"

## Step 2: Deploy to Vercel

### Option A: Deploy via Vercel Dashboard

1. Go to [vercel.com](https://vercel.com) and sign in
2. Click "New Project"
3. Import your GitHub repository
4. Vercel will automatically detect it's a Node.js project
5. Set the root directory to your backend folder if needed
6. Click "Deploy"

### Option B: Deploy via Vercel CLI

1. Install Vercel CLI:

   ```bash
   npm i -g vercel
   ```

2. Login to Vercel:

   ```bash
   vercel login
   ```

3. Deploy from your project directory:
   ```bash
   vercel
   ```

## Step 3: Configure Environment Variables

1. Go to your project dashboard on Vercel
2. Navigate to "Settings" > "Environment Variables"
3. Add the following variables:

   | Name         | Value                          | Environment |
   | ------------ | ------------------------------ | ----------- |
   | `MONGO_URI`  | Your MongoDB connection string | Production  |
   | `JWT_SECRET` | Your JWT secret key            | Production  |
   | `NODE_ENV`   | production                     | Production  |

## Step 4: Test Your Deployment

After deployment, test these endpoints:

- `GET /` - Should return API status
- `GET /api/health` - Health check endpoint
- `POST /api/auth/register` - Test user registration
- `POST /api/auth/login` - Test user login

## Step 5: Update Your Frontend

Update your Flutter app's API base URL to point to your Vercel deployment:

```dart
// Replace with your actual Vercel URL
const String baseUrl = 'https://your-project-name.vercel.app';
```

## Important Notes

1. **Serverless Functions**: Vercel runs your API as serverless functions, which means:

   - Cold starts may cause initial delays
   - Database connections are established per request
   - No persistent state between requests

2. **MongoDB Connection**: The app handles MongoDB connection per request to work with serverless architecture

3. **CORS**: The API is configured to allow all origins. For production, consider restricting this to your frontend domain

4. **Logs**: View logs in the Vercel dashboard under "Functions" tab

## Troubleshooting

### Common Issues:

1. **"Database service temporarily unavailable" Error**:

   - Check if `MONGO_URI` is set in Vercel environment variables
   - Verify MongoDB Atlas network access allows Vercel IPs (`0.0.0.0/0`)
   - Ensure MongoDB cluster is not paused
   - Test connection string with MongoDB Compass
   - See `DATABASE_TROUBLESHOOTING.md` for detailed solutions

2. **Environment Variables Not Working**:

   - Ensure variables are set in Vercel dashboard
   - Redeploy after adding environment variables
   - Check variable names match exactly (case-sensitive)

3. **API Routes Not Working**:

   - Check the Vercel function logs
   - Ensure your routes are properly defined
   - Verify the `vercel.json` configuration
   - Test with `/api/health` endpoint first

4. **Connection Timeouts**:
   - MongoDB Atlas region should be close to Vercel deployment region
   - Check MongoDB Atlas status page
   - Consider upgrading from free tier for better performance

### Quick Diagnostics:

Test your deployment health:

```bash
curl https://your-app.vercel.app/api/health
```

Expected response should show `"database.connected": true`

### Getting Help:

- Check Vercel function logs in the dashboard
- Use `vercel logs` command for CLI logs
- Test locally first with `vercel dev`
- Review `DATABASE_TROUBLESHOOTING.md` for database-specific issues

## Local Development with Vercel

To test the serverless setup locally:

```bash
# Install Vercel CLI if not already installed
npm i -g vercel

# Run in development mode
vercel dev
```

This will simulate the Vercel environment locally.

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **JWT Secret**: Use a strong, random string for JWT_SECRET
3. **MongoDB**: Use MongoDB Atlas with proper authentication
4. **CORS**: Restrict origins in production
5. **Rate Limiting**: Consider adding rate limiting for production use

## Performance Tips

1. **Database Connection**: The current setup connects to MongoDB on each request, which is suitable for serverless
2. **Caching**: Consider implementing caching for frequently accessed data
3. **Error Handling**: Comprehensive error handling is already implemented

Your API should now be successfully deployed on Vercel! 🚀
