const mongoose = require("mongoose");
const validator = require("validator");
const bcrypt = require("bcryptjs");

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please provide your name"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Please provide your email"],
      unique: true,
      lowercase: true,
      validate: [validator.isEmail, "Please provide a valid email"],
    },
    phoneNumber: {
      type: String,
      trim: true,
    },
    password: {
      type: String,
      required: function () {
        // Only required for regular signup, not when admin creates a user
        return !this.isAdminCreated;
      },
      minlength: 6,
      select: false,
      default: function () {
        // Set default password when admin creates a user
        return this.isAdminCreated ? "password123" : undefined;
      },
    },
    passwordConfirm: {
      type: String,
      required: function () {
        // Only required for regular signup, not when admin creates a user
        return !this.isAdminCreated;
      },
      validate: {
        // This only works on CREATE and SAVE
        validator: function (el) {
          // Skip validation if admin created
          if (this.isAdminCreated) return true;
          return el === this.password;
        },
        message: "Passwords are not the same",
      },
    },
    isAdminCreated: {
      type: Boolean,
      default: false,
      select: false, // Don't send this to client
    },
    role: {
      type: String,
      enum: ["user", "admin"],
      default: "user",
    },
    active: {
      type: Boolean,
      default: true,
      select: false,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtual populate for expenses
userSchema.virtual("expenses", {
  ref: "Expense",
  foreignField: "user",
  localField: "_id",
});

// Hash the password before saving
userSchema.pre("save", async function (next) {
  // Only run this function if password was modified
  if (!this.isModified("password")) return next();

  // Hash the password with cost of 12
  this.password = await bcrypt.hash(this.password, 12);

  // Delete passwordConfirm field
  this.passwordConfirm = undefined;
  next();
});

// Check if password is correct
userSchema.methods.correctPassword = async function (
  candidatePassword,
  userPassword
) {
  return await bcrypt.compare(candidatePassword, userPassword);
};

const User = mongoose.model("User", userSchema);

module.exports = User;
