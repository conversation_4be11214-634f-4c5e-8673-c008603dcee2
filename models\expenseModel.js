const mongoose = require('mongoose');

const expenseSchema = new mongoose.Schema(
  {
    description: {
      type: String,
      required: [true, 'An expense must have a description'],
      trim: true,
    },
    amount: {
      type: Number,
      required: [true, 'An expense must have an amount'],
      min: [0, 'Amount must be positive'],
    },
    date: {
      type: Date,
      required: [true, 'An expense must have a date'],
      default: Date.now,
    },
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'An expense must belong to a user'],
    },
    category: {
      type: String,
      enum: ['food', 'rent', 'utilities', 'entertainment', 'other'],
      default: 'other',
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Index for faster queries
expenseSchema.index({ user: 1, date: -1 });

// Populate user when finding expenses
expenseSchema.pre(/^find/, function (next) {
  this.populate({
    path: 'user',
    select: 'name email',
  });
  next();
});

const Expense = mongoose.model('Expense', expenseSchema);

module.exports = Expense;
