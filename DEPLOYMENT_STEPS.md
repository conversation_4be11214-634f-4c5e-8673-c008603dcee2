# Quick Deployment Steps for Vercel

## Prerequisites
- Vercel account created
- MongoDB Atlas cluster running
- Code pushed to GitHub (recommended)

## Step 1: Deploy to Vercel

### Option A: GitHub Integration (Recommended)
1. Go to [vercel.com](https://vercel.com)
2. Click "New Project"
3. Import your GitHub repository
4. Vercel will detect it's a Node.js project
5. Deploy

### Option B: Vercel CLI
```bash
# Install Vercel CLI globally
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod
```

## Step 2: Set Environment Variables in Vercel

1. Go to your Vercel project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add these variables for **Production**, **Preview**, and **Development**:

| Variable | Value |
|----------|-------|
| `MONGO_URI` | `mongodb+srv://sumankumarghosh:<EMAIL>/expense?retryWrites=true&w=majority` |
| `JWT_SECRET` | `room_expense_tracker_secret_key_2024` |
| `NODE_ENV` | `production` |

## Step 3: Configure MongoDB Atlas

1. **Go to MongoDB Atlas Dashboard**
2. **Navigate to "Network Access"**
3. **Click "Add IP Address"**
4. **Add `0.0.0.0/0`** (allows all IPs - required for Vercel)
5. **Ensure cluster is running** (not paused)

## Step 4: Test Deployment

After setting environment variables, **redeploy** your project:

1. In Vercel dashboard, go to **Deployments**
2. Click **"Redeploy"** on the latest deployment
3. Test your API:

```bash
# Test health endpoint
curl https://your-app-name.vercel.app/api/health

# Expected response should show:
# "database": { "connected": true }
```

## Step 5: Verify Database Connection

Your health endpoint should return:
```json
{
  "status": "healthy",
  "database": {
    "status": "connected",
    "connected": true
  }
}
```

## Troubleshooting

If you still get "Database service temporarily unavailable":

1. **Check Vercel Function Logs**:
   - Go to Vercel dashboard → Functions → View logs
   - Look for environment variable and connection errors

2. **Verify Environment Variables**:
   ```bash
   vercel env ls
   ```

3. **Test MongoDB Connection**:
   - Use MongoDB Compass with your connection string
   - Ensure cluster is not paused

4. **Redeploy After Changes**:
   - Always redeploy after changing environment variables
   - Environment changes don't take effect until redeployment

## Common Issues

- **"MONGO_URI not found"**: Environment variables not set in Vercel
- **Connection timeout**: MongoDB Atlas network access not configured
- **Cluster paused**: Free tier clusters pause after inactivity
- **Wrong database name**: Check the database name in your connection string

## Quick Commands

```bash
# Check if project is linked to Vercel
vercel ls

# Link project to Vercel
vercel link

# View environment variables
vercel env ls

# View deployment logs
vercel logs

# Redeploy
vercel --prod
```

Remember: After setting environment variables in Vercel, you MUST redeploy for changes to take effect!
